<template>
    <nav class="w-full h-[60px] bg-[#172333] flex justify-center items-center bg-blue-500 text-white py-2">
        <ul class="flex space-x-10 pr-10">
            <li class="nav-item"><router-link to="/public-place-monitor">公共场所在线监测</router-link></li>
            <li class="nav-item"><router-link to="/radiation-health-monitor">放射卫生在线监测</router-link></li>
        </ul>
        <!-- 中间区域 -->
        <ul>
            <li class="text-3xl"><router-link to="/">漳平市卫生监督管理系统</router-link></li>
        </ul>
        <!-- 右侧区域 -->
        <ul class="flex space-x-10 pl-10">
            <li class="nav-item"><router-link to="/occupational-hazard-monitor">职业病危害因素在线监测</router-link></li>
            <li class="nav-item"><router-link to="/real-time-video-monitor">实时视频监控</router-link></li>
            <li class="nav-item"><router-link to="/support-app-management">支撑应用管理</router-link></li>
        </ul>
    </nav>
</template>

<script setup>
// 可在这里写一些交互逻辑，比如激活态样式控制等（结合路由元信息或当前路由判断）
import { useRouter } from 'vue-router'
const router = useRouter()
// 若需要动态判断激活项，可监听 router.currentRoute
</script>

<style scoped>
/* 导航项公共样式（按钮化样式） */
.nav-item {
    /* 基础样式 */
    color: white;
    /* 文字颜色 */
    padding: 0 15px;
    /* 左右内边距，配合宽度实现总宽度179px */
    height: 41px;
    /* 高度 */
    line-height: 41px;
    /* 垂直居中 */
    border-radius: 4px;
    /* 圆角 */
    transition: all 0.3s ease;
    /* 过渡动画 */

    /* 去除默认列表样式 */
    list-style: none;
}

/* 选中状态样式（通过路由激活类名 .router-link-exact-active 或 .router-link-active 匹配） */
.router-link-exact-active .nav-item,
.router-link-active .nav-item {
    width: 179px;
    /* 选中时固定宽度 */
    background: #1890FF;
    /* 选中背景色 */
    border: 0 solid #0E050B;
    /* 边框 */
}

/* 非选中状态鼠标悬停样式（可选增强交互） */
.nav-item:hover {
    background: rgba(24, 144, 255, 0.1);
    /* 浅蓝色悬停背景 */
}

/* 调整列表项间距（覆盖 Tailwind 的 space-x-10） */
nav ul:first-child,
/* 左侧导航 */
nav ul:last-child {
    /* 右侧导航 */
    margin-left: 30px;
    /* 左侧导航与中间标题的间距 */
    margin-right: 30px;
    /* 右侧导航与中间标题的间距 */
}

/* 中间标题单独处理（避免应用按钮样式） */
nav ul:nth-child(2) li {
    margin: 0 30px;
    /* 中间标题左右间距 */
}
</style>